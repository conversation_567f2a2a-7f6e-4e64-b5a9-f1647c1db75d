/***************************************************************************
 *
 * Copyright (c) 2020 JD.com, Inc. All Rights Reserved
 *
 **************************************************************************/

package com.jdx.rover.monitor.enums;

import com.jdx.rover.monitor.api.domain.enums.VehicleAlarmEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 车辆报警分类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
@ToString
public enum AlarmEventCategoryEnum {
  /**
   * <p>
   * The enumerate alarm event type and solution type of vehicle.
   * </p>
   */
  VEHICLE_CRASH("防撞", Arrays.asList(VehicleAlarmEnum.VEHICLE_CRASH)),
  MANUAL_REPORT("人工告警", Arrays.asList(VehicleAlarmEnum.MANUAL_REPORT)),
  SENSOR_ERROR("传感器异常", Arrays.asList(VehicleAlarmEnum.SENSOR_ERROR, VehicleAlarmEnum.LOCALIZATION_ERROR)),
  BOOT_ERROR("开机异常", Arrays.asList(VehicleAlarmEnum.BOOT_ABNORMAL, VehicleAlarmEnum.BOOT_FAIL,
      VehicleAlarmEnum.BOOT_TIMEOUT, VehicleAlarmEnum.BOOT_ABNORMAL_FAIL)),
  INTERSECTION_ERROR("路口异常", Arrays.asList(VehicleAlarmEnum.PASS_NO_SIGNAL_INTERSECTION,
      VehicleAlarmEnum.VEHICLE_STOP_TRAFFICLIGHT_TAKEUP, VehicleAlarmEnum.VEHICLE_STOP_TRAFFICLIGHT_FAIL,
      VehicleAlarmEnum.VEHICLE_STOP_TRAFFICLIGHT_ADJUST, VehicleAlarmEnum.VEHICLE_STOP_INTERSECTION_STUCK)),
  VEHICLE_STOP("停车", Arrays.asList(VehicleAlarmEnum.VEHICLE_STOP_BUTTON, VehicleAlarmEnum.GATE_STUCK,
          VehicleAlarmEnum.VEHICLE_STOP_STOP_TAKEUP, VehicleAlarmEnum.VEHICLE_STOP_STOP_ADJUST,
          VehicleAlarmEnum.VEHICLE_STOP_FATAL, VehicleAlarmEnum.VEHICLE_STOP_GUARDIAN,
          VehicleAlarmEnum.VEHICLE_STOP_TIMEOUT, VehicleAlarmEnum.PARK_HARD,
          VehicleAlarmEnum.CPU_HIGH_TEMPERATURE, VehicleAlarmEnum.ROUTING_PLAN_FAIL)),
  CLOUD_WARN("云端预警", Arrays.asList(VehicleAlarmEnum.ATTENTION_REGION_WARN, VehicleAlarmEnum.PARKING_REGION_WARN)),
  WHEEL_ALARM("胎压", Arrays.asList(VehicleAlarmEnum.LOW_PRESSURE, VehicleAlarmEnum.PRESSURE_SHARP_DECREASE)),
  BATTERY("电量", Arrays.asList(VehicleAlarmEnum.LOW_BATTERY));

  /**
   * <p>
   * 一级告警名
   * </p>
   */
  private String name;

  /**
   * <p>
   * 二级告警
   * </p>
   */
  private List<VehicleAlarmEnum> child;

}
