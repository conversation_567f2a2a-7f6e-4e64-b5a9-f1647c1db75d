/*
 * Copyright (c) 2024 JD.com, Inc. All Rights Reserved.
 */

package com.jdx.rover.monitor.dto.drawer;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 注
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11
 */
@Data
public class DrawerMqttDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 当前座席ID
     */
    private String cockpitNumber;

    /**
     * 记录时间
     */
    private Date recordTime;

    /**
     * tile 列表
     */
    private List<Issue> issueList;

    /**
     * 响应数据列表
     */
    @Data
    @NoArgsConstructor
    public static class Issue {
        /**
         * 车号
         */
        private String vehicleName;

        /**
         * 站点名称
         */
        private String stationName;

        /**
         * 车辆模式
         */
        private String vehicleState;

        /**
         * 系统状态
         */
        private String systemState;

        /**
         * 调度状态
         */
        private String scheduleState;

        /**
         * 速度
         */
        private Double speed;

        /**
         * 工单编号
         */
        private String issueNumber;
        /**
         * 工单状态
         */
        private String issueStatus;
        /**
         * 是否催单
         */
        private Boolean issueReminder;
        /**
         * 工单类型
         */
        private String issueType;
        /**
         * 工单开始时间
         */
        private Date issueStartTime;
        /**
         * 工单挂起时间(最后一次)
         */
        private Date issuePendingTime;
        /**
         * 工单来源
         */
        private String issueSource;
        /**
         * 派单用户
         */
        private String issueSourceUser;
        /**
         * 当前事件个数
         */
        private Integer eventCount;
        /**
         * 是否为新工单
         */
        private Boolean newIssue;
        /**
         * 是否报备提醒
         */
        private Boolean reportReminder = false;
        /**
         * 是否紧急工单
         */
        private Boolean emergencyIssue;
        /**
         * 工单事件列表
         */
        private List<IssueEvent> issueEventList;
        /**
         * 工单下事件生成记录列表
         */
        private List<EventRecord> issueEventRecordList;
        /**
         * 问题提报信息
         */
        private BugReport bugReport;
        /**
         * 动态预警信息备注
         */
        private String mapVariableRemark;
    }

    /**
     * 工单事件
     */
    @Data
    @NoArgsConstructor
    public static class IssueEvent {
        /**
         * 事件开始时间
         */
        private Date startTime;

        /**
         * 事件类型
         */
        private String eventType;
    }

    /**
     * 工单下事件生成记录
     */
    @Data
    @NoArgsConstructor
    public static class EventRecord {

        /**
         * 上报时间
         */
        private Date reportTime;

        /**
         * 描述信息
         */
        private String description;
    }

    /**
     * 问题提报记录
     */
    @Data
    @NoArgsConstructor
    public static class BugReport {

        /**
         * 开始时间
         */
        private Date startTime;

        /**
         * 结束时间
         */
        private Date endTime;

        /**
         * 问题描述
         */
        private String description;

        /**
         * 附件列表
         */
        private List<Attachment> attachmentList;

        /**
         * 附件
         */
        @Data
        public static class Attachment {

            /**
             * 文件类型
             */
            private String type;

            /**
             * 文件key
             */
            private String fileKey;

            /**
             * 文件url
             */
            private String url;
        }
    }
}