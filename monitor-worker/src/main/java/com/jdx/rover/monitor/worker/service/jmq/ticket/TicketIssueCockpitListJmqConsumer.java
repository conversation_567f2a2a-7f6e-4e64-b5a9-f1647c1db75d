/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.ticket;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.api.domain.enums.VehicleEventEnum;
import com.jdx.rover.monitor.entity.user.UserIssueDO;
import com.jdx.rover.monitor.manager.drawer.DrawerMqttManager;
import com.jdx.rover.monitor.repository.redis.user.UserIssueRepository;
import com.jdx.rover.monitor.service.area.EarlyWarningAreaService;
import com.jdx.rover.monitor.service.area.EarlyWarningAreaService.AreaLocation;
import com.jdx.rover.monitor.service.cockpit.CockpitChangeService;
import com.jdx.rover.ticket.api.kafka.CockpitIssueInfoDTO;
import com.jdx.rover.ticket.api.kafka.IssueCockpitListDTO;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 座席工单列表监听
 *
 * <AUTHOR>
 * @date 2024/12/23
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TicketIssueCockpitListJmqConsumer implements MessageListener {

    /**
     * 座席工单列表监听服务。
     */
    private final CockpitChangeService cockpitChangeService;

    /**
     * 用户工单缓存。
     */
    private final UserIssueRepository userIssueRepository;

    /**
     * 用于推送工单抽屉消息的MQTT管理器。
     */
    private final DrawerMqttManager drawerMqttManager;

    /**
     * 座席工单列表
     *
     * @param messages 消息列表
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理工单列表消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理message
     *
     * @param message message
     */
    private void handleOneMessage(String message) {
        log.info("收到座席下待处理工单列表信息{}", message);
        IssueCockpitListDTO issueCockpitListDTO = JsonUtils.readValue(message, IssueCockpitListDTO.class);
        if (issueCockpitListDTO == null || StringUtils.isBlank(issueCockpitListDTO.getCockpitNumber()) || StringUtils.isBlank(issueCockpitListDTO.getUserName())) {
            log.error("IssueCockpitListDTO data invalid {}.", message);
            return;
        }
        // 1、工单缓存更新
        UserIssueDO userIssueDO = buildUserIssueDO(issueCockpitListDTO);
        userIssueRepository.set(userIssueDO);
        // 2、结单后休息判断
        cockpitChangeService.checkRest(issueCockpitListDTO);
        // 3、立即推送工单抽屉消息
        drawerMqttManager.pushDrawer(userIssueDO.getUserName());
    }

    /**
     * buildUserIssueDO
     *
     * @param issueCockpitListDTO issueCockpitListDTO
     * @return UserIssueDO
     */
    private UserIssueDO buildUserIssueDO(IssueCockpitListDTO issueCockpitListDTO) {
        List<UserIssueDO.Issue> issueList = new ArrayList<>();
        for (CockpitIssueInfoDTO issueInfoDTO : issueCockpitListDTO.getList()) {
            UserIssueDO.Issue issue = new UserIssueDO.Issue();
            issue.setIssueNumber(issueInfoDTO.getIssueNumber());
            issue.setVehicleName(issueInfoDTO.getVehicleName());
            issue.setStationName(issueInfoDTO.getStationName());
            issue.setIssueStatus(issueInfoDTO.getIssueStatus());
            issue.setIssueReminder(issueInfoDTO.getIssueReminder());
            issue.setIssueType(issueInfoDTO.getIssueType());
            issue.setIssueStartTime(issueInfoDTO.getIssueStartTime());
            issue.setIssuePendingTime(issueInfoDTO.getIssuePendingStartTime());
            issue.setIssueSource(issueInfoDTO.getIssueSource());
            issue.setIssueSourceUser(issueInfoDTO.getIssueSourceUser());
            issue.setEmergencyIssue(issueInfoDTO.getEmergencyIssue());
            issue.setEventCount(issueInfoDTO.getEventCount());
            issue.setNewIssue(issueInfoDTO.getNewIssue());
            issue.setReportReminder(issueInfoDTO.getReportReminder());
            if (CollectionUtils.isNotEmpty(issueInfoDTO.getIssueEventList())) {
                issue.setIssueEventList(issueInfoDTO.getIssueEventList().stream().map(event -> {
                    UserIssueDO.IssueEvent issueEvent = new UserIssueDO.IssueEvent();
                    issueEvent.setEventType(event.getEventType());
                    issueEvent.setStartTime(event.getStartTime());
                    if (VehicleEventEnum.ATTENTION_REGION_WARN.getValue().equals(event.getEventType())) {
                        Map<String, Set<AreaLocation>> vehicleInAreaMap = EarlyWarningAreaService.getVehicleInAreaMap();
                        AreaLocation areaLocation = vehicleInAreaMap.get(issue.getVehicleName()).iterator().next();
                        if (null != areaLocation) {
                            issueEvent.setMapVariableRemark(areaLocation.getRemark());
                        }
                    }
                    return issueEvent;
                }).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(issueInfoDTO.getIssueEventRecordList())) {
                issue.setIssueEventRecordList(issueInfoDTO.getIssueEventRecordList().stream().map(event -> {
                    UserIssueDO.EventRecord eventRecord = new UserIssueDO.EventRecord();
                    eventRecord.setReportTime(event.getReportTime());
                    eventRecord.setDescription(event.getDescription());
                    return eventRecord;
                }).collect(Collectors.toList()));
            }
            if (null != issueInfoDTO.getBugReport()) {
                CockpitIssueInfoDTO.BugReport bugReport = issueInfoDTO.getBugReport();
                UserIssueDO.BugReport issueBugReport = new UserIssueDO.BugReport();
                issueBugReport.setStartTime(bugReport.getStartTime());
                issueBugReport.setEndTime(bugReport.getEndTime());
                issueBugReport.setDescription(bugReport.getDescription());
                if (CollUtil.isNotEmpty(bugReport.getAttachmentList())) {
                    List<UserIssueDO.BugReport.Attachment> attachmentList = Lists.newArrayListWithCapacity(bugReport.getAttachmentList().size());
                    bugReport.getAttachmentList().forEach(attachment -> {
                        UserIssueDO.BugReport.Attachment attachmentInfo = new UserIssueDO.BugReport.Attachment();
                        attachmentInfo.setType(attachment.getType());
                        attachmentInfo.setFileKey(attachment.getFileKey());
                        attachmentList.add(attachmentInfo);
                    });
                    issueBugReport.setAttachmentList(attachmentList);
                }
                issue.setBugReport(issueBugReport);
            }
            issueList.add(issue);
        }
        UserIssueDO userIssueDO = new UserIssueDO();
        userIssueDO.setUserName(issueCockpitListDTO.getUserName());
        userIssueDO.setCockpitNumber(issueCockpitListDTO.getCockpitNumber());
        userIssueDO.setRecordTime(issueCockpitListDTO.getRecordTime());
        userIssueDO.setIssueList(issueList);
        return userIssueDO;
    }

}